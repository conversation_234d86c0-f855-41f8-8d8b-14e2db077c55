'use client';

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';

type Language = 'en' | 'zh';

interface LanguageContextType {
  language: Language;
  toggleLanguage: () => void;
  t: (key: string, fallback: string) => string;
}

// Comprehensive translations
const translations: Record<Language, Record<string, string>> = {
  en: {
    // Navigation
    innerText: 'Inner Text',
    cover: 'Cover',
    endpapers: 'Endpapers',

    // Card Titles
    jobSpecsTitle: 'Job Specifications',
    prodParamsTitle: 'Production Parameters',
    coverProdParamsTitle: 'Cover Prod. Parameters',
    endpaperProdParamsTitle: 'Endpaper Prod. Parameters',
    converterTitle: 'Unit Converter',
    paperOptionsTitle: 'Paper Options',
    resultsTitle: 'Calculation Results',

    // Job Specifications
    pageHeightLabel: 'Page Height (mm)',
    pageHeightLabelIn: 'Page Height (in)',
    pageWidthLabel: 'Page Width (mm)',
    pageWidthLabelIn: 'Page Width (in)',
    totalPagesLabel: 'Total Pages',
    quantityLabel: 'Quantity',
    bindingLabel: 'Binding Method',
    spoilageLabel: 'Spoilage (%)',

    // Binding Methods
    bindingSaddleStitch: 'Saddle Stitch',
    bindingPerfectBound: 'Paperback',
    bindingWireO: 'Wire-O / Coil',
    bindingSectionSewn: 'Section Sewn',
    bindingCaseBound: 'Case Bound (Hardcover)',
    bindingSinglePage: 'Single Page / Flat Sheet',

    // Production Parameters
    bleedLabel: 'Bleed (mm)',
    gripperLabel: 'Gripper (mm)',
    colorBarLabel: 'Color Bar (mm)',
    gutterLabel: 'Gutter (mm)',
    sideLipLabel: 'Side Lip',
    alignmentModeLabel: 'Grain Alignment',
    alignmentModeAligned: 'Aligned',
    alignmentModeMisaligned: 'Misaligned',

    // Cover Specific
    trimHeightLabel: 'Trim Height (mm)',
    trimWidthLabel: 'Trim Width (mm)',
    spineThicknessLabel: 'Spine Thickness (mm)',
    coverTypeLabel: 'Cover Type',
    coverTypePaperback: 'Paperback',
    coverTypeHardcover: 'Hardcover',
    coverTypeDustJacket: 'Dust Jacket',
    turnInAllowanceLabel: 'Turn-in Allowance (mm)',
    flapWidthLabel: 'Flap Width (mm)',

    // Unit Converter
    mmToInLabel: 'mm to inches',
    inToMmLabel: 'inches to mm',
    gsm2BasisLabel: 'GSM to Basis Weight',
    basis2GsmLabel: 'Basis Weight to GSM',
    convertButton: 'Convert',

    // Paper Options
    addPaperButton: 'Add Paper',
    paperNameLabel: 'Paper Name',
    sourceLabel: 'Source',
    sheetHeightLabel: 'Sheet Height (mm)',
    sheetWidthLabel: 'Sheet Width (mm)',
    grainDirLabel: 'Grain Direction',
    gsmLabel: 'GSM',
    caliperLabel: 'Caliper (µm)',
    costReamLabel: 'Cost per Ream',
    costTonneLabel: 'Cost per Tonne',
    calculateButton: 'Calculate',

    // Results
    mostEfficientBadge: 'Lowest Waste %',
    resInputSheet: 'Input Sheet (mm)',
    resPressSize: 'Press Size (mm)',
    resUsableArea: 'Usable Area (mm)',
    resGrainAlignment: 'Grain Alignment',
    resUntrimmedPage: 'Untrimmed Page (mm)',
    resImposedArea: 'Imposed Area (mm)',
    resLayoutFit: 'Layout Fit (Down x Across)',
    resPagesPerSide: 'Pages/Side',
    resSheetUtilization: 'Sheet Utilization %',
    resResultingSig: 'Resulting Sig.',

    // Placeholders
    placeholderPageHeight: 'e.g., 225',
    placeholderPageWidth: 'e.g., 150',
    placeholderTotalPages: 'e.g., 320',
    placeholderQuantity: 'e.g., 1000',
    placeholderSpoilage: 'e.g., 5',
    placeholderBleed: 'e.g., 3',
    placeholderGripper: 'e.g., 10',
    placeholderColorBar: 'e.g., 5',
    placeholderGutter: 'e.g., 6',
  },
  zh: {
    // Navigation
    innerText: '内文',
    cover: '封面',
    endpapers: '护页',

    // Card Titles
    jobSpecsTitle: '工作规格',
    prodParamsTitle: '生产参数',
    coverProdParamsTitle: '封面生产参数',
    endpaperProdParamsTitle: '护页生产参数',
    converterTitle: '单位转换器',
    paperOptionsTitle: '纸张选项',
    resultsTitle: '计算结果',

    // Job Specifications
    pageHeightLabel: '页面高度 (毫米)',
    pageHeightLabelIn: '页面高度 (英寸)',
    pageWidthLabel: '页面宽度 (毫米)',
    pageWidthLabelIn: '页面宽度 (英寸)',
    totalPagesLabel: '总页数',
    quantityLabel: '数量',
    bindingLabel: '装订方式',
    spoilageLabel: '损耗率 (%)',

    // Binding Methods
    bindingSaddleStitch: '骑马钉',
    bindingPerfectBound: '无线胶装',
    bindingWireO: '线圈装订',
    bindingSectionSewn: '锁线装订',
    bindingCaseBound: '精装',
    bindingSinglePage: '单页/平张',

    // Production Parameters
    bleedLabel: '出血 (毫米)',
    gripperLabel: '咬口 (毫米)',
    colorBarLabel: '色标 (毫米)',
    gutterLabel: '装订边距 (毫米)',
    sideLipLabel: '侧唇',
    alignmentModeLabel: '纹理对齐',
    alignmentModeAligned: '对齐',
    alignmentModeMisaligned: '不对齐',

    // Cover Specific
    trimHeightLabel: '裁切高度 (毫米)',
    trimWidthLabel: '裁切宽度 (毫米)',
    spineThicknessLabel: '书脊厚度 (毫米)',
    coverTypeLabel: '封面类型',
    coverTypePaperback: '平装',
    coverTypeHardcover: '精装',
    coverTypeDustJacket: '书衣',
    turnInAllowanceLabel: '包边余量 (毫米)',
    flapWidthLabel: '折页宽度 (毫米)',

    // Unit Converter
    mmToInLabel: '毫米转英寸',
    inToMmLabel: '英寸转毫米',
    gsm2BasisLabel: '克重转基重',
    basis2GsmLabel: '基重转克重',
    convertButton: '转换',

    // Paper Options
    addPaperButton: '添加纸张',
    paperNameLabel: '纸张名称',
    sourceLabel: '来源',
    sheetHeightLabel: '纸张高度 (毫米)',
    sheetWidthLabel: '纸张宽度 (毫米)',
    grainDirLabel: '纹理方向',
    gsmLabel: '克重',
    caliperLabel: '厚度 (微米)',
    costReamLabel: '令价',
    costTonneLabel: '吨价',
    calculateButton: '计算',

    // Results
    mostEfficientBadge: '最低浪费率',
    resInputSheet: '输入纸张 (毫米)',
    resPressSize: '印刷机尺寸 (毫米)',
    resUsableArea: '可用面积 (毫米)',
    resGrainAlignment: '纹理对齐',
    resUntrimmedPage: '未裁切页面 (毫米)',
    resImposedArea: '拼版面积 (毫米)',
    resLayoutFit: '排版 (下 x 横)',
    resPagesPerSide: '每面页数',
    resSheetUtilization: '纸张利用率 %',
    resResultingSig: '签名结果',

    // Placeholders
    placeholderPageHeight: '例如: 225',
    placeholderPageWidth: '例如: 150',
    placeholderTotalPages: '例如: 320',
    placeholderQuantity: '例如: 1000',
    placeholderSpoilage: '例如: 5',
    placeholderBleed: '例如: 3',
    placeholderGripper: '例如: 10',
    placeholderColorBar: '例如: 5',
    placeholderGutter: '例如: 6',
  }
};

const LanguageContext = createContext<LanguageContextType | undefined>(undefined);

// Function to apply translations to all elements with data-translate-key
const applyTranslations = (lang: Language) => {
  if (typeof document === 'undefined') return; // Skip during SSR

  // Get all elements with data-translate-key attribute
  const elements = document.querySelectorAll('[data-translate-key]');

  elements.forEach(element => {
    const key = element.getAttribute('data-translate-key');
    if (!key) return;

    const translation = translations[lang][key];
    if (!translation) return;

    // Handle different element types
    if (element.tagName === 'INPUT' || element.tagName === 'TEXTAREA') {
      // For input placeholders
      if (element.hasAttribute('placeholder')) {
        const placeholderKey = element.getAttribute('data-placeholder-translate-key');
        if (placeholderKey && translations[lang][placeholderKey]) {
          (element as HTMLInputElement).placeholder = translations[lang][placeholderKey];
        }
      }
    } else if (element.tagName === 'OPTION') {
      // For select options
      element.textContent = translation;
    } else {
      // For regular elements
      element.textContent = translation;
    }
  });
};

export function LanguageProvider({ children }: { children: ReactNode }) {
  const [language, setLanguage] = useState<Language>('en');

  // Apply translations whenever language changes
  useEffect(() => {
    applyTranslations(language);
  }, [language]);

  useEffect(() => {
    // Check for saved language preference
    const savedLanguage = localStorage.getItem('language') as Language | null;
    if (savedLanguage && (savedLanguage === 'en' || savedLanguage === 'zh')) {
      setLanguage(savedLanguage);

      // Update the UI to reflect the initial language
      setTimeout(() => {
        const langSwitch = document.querySelector('.lang-switch');
        if (langSwitch) {
          if (savedLanguage === 'zh') {
            langSwitch.classList.add('active');
          } else {
            langSwitch.classList.remove('active');
          }
        }
      }, 0);
    }

    // Set up a mutation observer to apply translations to new elements
    const observer = new MutationObserver((mutations) => {
      let shouldApplyTranslations = false;

      mutations.forEach(mutation => {
        if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
          // Check if any added nodes have data-translate-key
          mutation.addedNodes.forEach(node => {
            if (node.nodeType === Node.ELEMENT_NODE) {
              const element = node as Element;
              if (element.querySelector('[data-translate-key]') || element.hasAttribute('data-translate-key')) {
                shouldApplyTranslations = true;
              }
            }
          });
        }
      });

      if (shouldApplyTranslations) {
        applyTranslations(language);
      }
    });

    observer.observe(document.body, { childList: true, subtree: true });

    return () => observer.disconnect();
  }, []);

  const toggleLanguage = () => {
    const newLanguage = language === 'en' ? 'zh' : 'en';
    setLanguage(newLanguage);
    localStorage.setItem('language', newLanguage);

    // Update the UI to reflect the language change
    const langSwitch = document.querySelector('.lang-switch');
    if (langSwitch) {
      if (newLanguage === 'zh') {
        langSwitch.classList.add('active');
      } else {
        langSwitch.classList.remove('active');
      }
    }

    // Apply translations immediately
    applyTranslations(newLanguage);
  };

  const t = (key: string, fallback: string): string => {
    return translations[language][key] || fallback;
  };

  return (
    <LanguageContext.Provider value={{ language, toggleLanguage, t }}>
      {children}
    </LanguageContext.Provider>
  );
}

export function useLanguage() {
  const context = useContext(LanguageContext);
  if (context === undefined) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
}
