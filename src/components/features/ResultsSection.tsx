'use client';

import React, { useState, useEffect } from 'react';
import { CalculationResult } from '@/utils/calculationEngine';
import ResultCard from './ResultCard';
import { motion, AnimatePresence } from 'framer-motion';

interface ResultsSectionProps {
  results: CalculationResult[];
  isLoading?: boolean;
  // TODO: Add translation function or context for labels
}

const ResultsSection: React.FC<ResultsSectionProps> = ({ results, isLoading = false }) => {
  const [isCollapsed, setIsCollapsed] = useState(false);
  const [bestOptionIndex, setBestOptionIndex] = useState<number | null>(null);

  // Find the best option based on lowest waste percentage or lowest cost
  useEffect(() => {
    if (!results || results.length === 0) {
      setBestOptionIndex(null);
      return;
    }

    // Filter out results with errors
    const validResults = results.filter(r => !r.error);
    if (validResults.length === 0) {
      setBestOptionIndex(null);
      return;
    }

    // Find the result with the lowest waste percentage
    let lowestWasteIndex = 0;
    let lowestWastePercent = validResults[0].wastePercent || 1;

    validResults.forEach((result, index) => {
      if (result.wastePercent !== undefined && result.wastePercent < lowestWastePercent) {
        lowestWastePercent = result.wastePercent;
        lowestWasteIndex = index;
      }
    });

    // Map back to the original results array index
    const originalIndex = results.findIndex(r => r === validResults[lowestWasteIndex]);
    setBestOptionIndex(originalIndex);
  }, [results]);

  // If no results or loading, show placeholder
  if (isLoading) {
    return (
      <div className="results-section-loading">
        <div className="loading-spinner"></div>
        <p>Calculating optimal layouts...</p>
      </div>
    );
  }

  if (!results || results.length === 0) {
    return null;
  }

  return (
    <AnimatePresence>
      <motion.div 
        className="results-section"
        initial={{ opacity: 0, height: 0 }}
        animate={{ opacity: 1, height: 'auto' }}
        exit={{ opacity: 0, height: 0 }}
        transition={{ duration: 0.3 }}
      >
        <div className="results-header">
          <h2 className="text-xl font-bold mb-4">Calculation Results</h2>
          <button 
            onClick={() => setIsCollapsed(!isCollapsed)}
            className="text-sm text-primary hover:text-primary-dark transition-colors"
          >
            {isCollapsed ? 'Show All Options' : 'Show Best Option Only'}
          </button>
        </div>

        <div className="results-grid-container">
          <div className="results-grid">
            {isCollapsed && bestOptionIndex !== null ? (
              // Show only the best option when collapsed
              <ResultCard 
                key={`result-${bestOptionIndex}`}
                result={results[bestOptionIndex]} 
                isBestOption={true}
              />
            ) : (
              // Show all options
              results.map((result, index) => (
                <ResultCard 
                  key={`result-${index}`}
                  result={result} 
                  isBestOption={index === bestOptionIndex}
                />
              ))
            )}
          </div>
        </div>
      </motion.div>
    </AnimatePresence>
  );
};

export default ResultsSection;
