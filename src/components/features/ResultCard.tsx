'use client';

import React from 'react';
import { CalculationResult } from '@/utils/calculationEngine'; // Assuming this path is correct

interface ResultCardProps {
  result: CalculationResult;
  isBestOption?: boolean; // To highlight the best option
  // TODO: Add translation function or context for labels
}

const ResultCard: React.FC<ResultCardProps> = ({ result, isBestOption }) => {
  const T = (key: string, fallback: string) => {
    // Basic placeholder for translation - replace with actual i18n logic
    const translations: Record<string, string> = {
      resErrorPrefix: "Error",
      na: "N/A",
      mostEfficientBadge: "Lowest Waste %",
      resInputSheet: "Input Sheet (mm)",
      resPressSize: "Press Size (mm)",
      resUsableArea: "Usable Area (mm)",
      resGrainAlignment: "Grain Alignment",
      grainAligned: "Aligned",
      grainMisaligned: "Misaligned",
      resUntrimmedPage: "Untrimmed Page (mm)",
      resImposedArea: "Imposed Area (mm)",
      resLayoutFit: "Layout Fit (Down x Across)",
      resPagesPerSide: "Pages/Side",
      resSheetUtilization: "Sheet Utilization %",
      resResultingSig: "Resulting Sig.",
      resTotalSheets: "Total Sheets",
      resCostPerSheet: "Cost/Sheet",
      resTotalCost: "Total Cost",
      resBookBlockThickness: "Book Block Thickness",
      resCostPerBook: "Cost Per Book",
      resSource: "Source",
      resInputGrain: "Input Grain",
      resGsm: "GSM",
      colCaliper: "Caliper",
    };
    return translations[key] || fallback;
  };

  const formatMmIn = (mm?: number) => {
    if (mm === undefined || isNaN(mm)) return T('na', 'N/A');
    const inches = (mm / 25.4).toFixed(2);
    return `${mm.toFixed(1)}mm (${inches}")`;  
  };

  const grainCls = result.grainAlignmentStatus === 'Aligned' ? 'grain-aligned' : result.grainAlignmentStatus === 'Misaligned' ? 'grain-misaligned' : '';

  return (
    <div className={`result-card relative rounded-xl shadow-lg border border-neutral-200 dark:border-neutral-700 overflow-hidden transition-all duration-300 hover:shadow-xl hover:translate-y-[-4px] ${isBestOption ? 'best-option border-primary/30 dark:border-primary/40' : ''}`}>
      {isBestOption && (
        <div className="absolute top-0 right-0 bg-gradient-to-r from-primary to-secondary text-white text-xs font-bold py-1 px-3 rounded-bl-lg z-10">
          {T('mostEfficientBadge', 'Lowest Waste %')}
        </div>
      )}
      
      <div className="p-5">
        <div className="mb-4">
          <h3 className="text-xl font-bold text-neutral-800 dark:text-neutral-100 mb-1 group-hover:text-primary transition-colors duration-300">
            {result.paperName}
          </h3>
          {result.error && (
            <span className="text-sm font-medium text-danger-light">
              ({T('resErrorPrefix', 'Error')})
            </span>
          )}
        </div>

        {result.error && result.errorMessageKey && (
          <p className="mb-2 text-sm font-medium text-red-500 dark:text-red-400">
            {T(result.errorMessageKey, result.errorMessageKey)}
          </p>
        )}

        <div className="mb-4 grid grid-cols-1 sm:grid-cols-2 gap-x-6 gap-y-4">
          <div><div className="result-label text-sm text-neutral-500 dark:text-neutral-400">{T('resInputSheet', 'Input Sheet (mm)')}</div><div className="result-value font-medium">{formatMmIn(result.sheetH_input)} H × {formatMmIn(result.sheetW_input)} W</div></div>
          <div><div className="result-label text-sm text-neutral-500 dark:text-neutral-400">{T('resPressSize', 'Press Size (mm)')}</div><div className="result-value font-medium">{formatMmIn(result.pressH)} H × {formatMmIn(result.pressW)} W</div></div>
          <div><div className="result-label text-sm text-neutral-500 dark:text-neutral-400">{T('resUsableArea', 'Usable Area (mm)')}</div><div className="result-value font-medium">{formatMmIn(result.usableH)} H × {formatMmIn(result.usableW)} W</div></div>
          <div><div className="result-label text-sm text-neutral-500 dark:text-neutral-400">{T('resGrainAlignment', 'Grain Alignment')}</div><div className={`result-value font-medium ${grainCls}`}>{result.grainAlignmentStatus || T('na', 'N/A')}</div></div>
          <div><div className="result-label text-sm text-neutral-500 dark:text-neutral-400">{T('resUntrimmedPage', 'Untrimmed Page (mm)')}</div><div className="result-value font-medium">{formatMmIn(result.layoutPageH)} H × {formatMmIn(result.layoutPageW)} W</div></div>
          <div><div className="result-label text-sm text-neutral-500 dark:text-neutral-400">{T('resImposedArea', 'Imposed Area (mm)')}</div><div className="result-value font-medium">{formatMmIn(result.occupiedHeight)} H × {formatMmIn(result.occupiedWidth)} W</div></div>
          <div><div className="result-label text-sm text-neutral-500 dark:text-neutral-400">{T('resLayoutFit', 'Layout Fit')}</div><div className="result-value font-medium">{result.error ? T('na', 'N/A') : `${result.winningLayoutDownPages || 0} × ${result.winningLayoutAcrossPages || 0}`}</div></div>
          <div><div className="result-label text-sm text-neutral-500 dark:text-neutral-400">{T('resPagesPerSide', 'Pages/Side')}</div><div className="result-value font-medium">{result.error || !result.maxItemsPerSide || result.maxItemsPerSide <= 0 ? T('na', 'N/A') : result.maxItemsPerSide}</div></div>
          <div><div className="result-label text-sm text-neutral-500 dark:text-neutral-400">{T('resSheetUtilization', 'Sheet Utilization %')}</div><div className="result-value font-medium">{result.error || result.maxItemsPerSide === 0 || result.wastePercent === undefined ? T('na', 'N/A') : `${((1 - result.wastePercent) * 100).toFixed(1)}%`}</div></div>
          <div><div className="result-label text-sm text-neutral-500 dark:text-neutral-400">{T('resResultingSig', 'Resulting Sig.')}</div><div className="result-value font-medium">{result.error || !result.pagesPerSheetOutput || result.pagesPerSheetOutput <= 0 ? T('na', 'N/A') : `${result.pagesPerSheetOutput}p`}</div></div>
          <div><div className="result-label text-sm text-neutral-500 dark:text-neutral-400">{T('resTotalSheets', 'Total Sheets')}</div><div className="result-value font-medium">{result.error || result.totalSheetsNeeded === undefined ? T('na', 'N/A') : result.totalSheetsNeeded.toLocaleString()}</div></div>
          <div><div className="result-label text-sm text-neutral-500 dark:text-neutral-400">{T('resCostPerSheet', 'Cost/Sheet')}</div><div className="result-value font-medium">{result.error || result.costPerSheet === undefined ? T('na', 'N/A') : `$${result.costPerSheet.toFixed(4)}`}</div></div>
        </div>
        
        <div className="col-span-1 sm:col-span-2 bg-gradient-to-r from-primary/5 to-secondary/5 dark:from-primary/10 dark:to-secondary/10 rounded-lg p-4 border border-primary/20 mb-4">
          <div className="result-label text-sm text-primary dark:text-primary-light">{T('resTotalCost', 'Total Cost')}</div>
          <div className="result-value text-2xl font-bold text-primary dark:text-primary-light">
            {result.error || result.totalCost === undefined || result.totalCost === Infinity ? T('na', 'N/A') : `$${result.totalCost.toFixed(2)}`}
          </div>
        </div>

        {(result.bookBlockThickness_mm !== undefined || result.costPerBook !== undefined) && (
          <div className="mt-4 grid grid-cols-1 sm:grid-cols-2 gap-x-6 gap-y-3 border-t border-primary/20 pt-4">
            {result.bookBlockThickness_mm !== undefined && (
                <div><div className="result-label text-sm text-neutral-500 dark:text-neutral-400">{T('resBookBlockThickness', 'Book Block Thickness')}</div><div className="result-value font-medium">{result.error || isNaN(result.bookBlockThickness_mm) ? T('na', 'N/A') : `${result.bookBlockThickness_mm.toFixed(2)} mm`}</div></div>
            )}
            {result.costPerBook !== undefined && (
                <div><div className="result-label text-sm text-neutral-500 dark:text-neutral-400">{T('resCostPerBook', 'Cost Per Book')}</div><div className="result-value font-medium">{result.error || isNaN(result.costPerBook) ? T('na', 'N/A') : `$${result.costPerBook.toFixed(4)}`}</div></div>
            )}
          </div>
        )}

        <div className="mt-4 border-t border-primary/20 pt-4 text-xs text-neutral-500 dark:text-neutral-400 bg-neutral-50/50 dark:bg-neutral-800/50 rounded-lg p-3">
          <div className="flex flex-wrap gap-x-4 gap-y-1">
            <span><strong>{T('resSource', 'Source')}:</strong> {result.source === 'Pre-Cut' ? 'Pre-Cut' : 'Roll'}</span>
            <span><strong>{T('resInputGrain', 'Input Grain')}:</strong> {result.grainDirInput}</span>
            <span><strong>{T('resGsm', 'GSM')}:</strong> {result.gsm || T('na', 'N/A')}g/m²</span>
            <span><strong>{T('colCaliper', 'Caliper').split(' ')[0]}:</strong> {result.caliperMicrons > 0 ? `${result.caliperMicrons.toFixed(0)} µm` : T('na', 'N/A')}</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ResultCard;
