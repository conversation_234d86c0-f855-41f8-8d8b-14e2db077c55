'use client';

import React, { useState, ChangeEvent } from 'react';
import {
  mmToIn,
  inToMm,
  ptToMicrons,
  micronsToPt,
  lbsToGsm,
  gsmToLbs,
  PaperWeightType,
} from '@/utils/conversions';
import CardWrapper from '@/components/ui/CardWrapper';

interface ConverterValues {
  lbs: string;
  gsm: string;
  pt: string;
  microns: string;
  inches: string;
  mm: string;
}

const initialConverterValues: ConverterValues = {
  lbs: '',
  gsm: '',
  pt: '',
  microns: '',
  inches: '',
  mm: '',
};

const UnitConverterCard = () => {
  const [values, setValues] = useState<ConverterValues>(initialConverterValues);
  const [paperType, setPaperType] = useState<PaperWeightType>('Text');
  const [updating, setUpdating] = useState<boolean>(false);

  const handlePaperTypeChange = (e: ChangeEvent<HTMLInputElement>) => {
    const newType = e.target.value as PaperWeightType;
    setPaperType(newType);
    // Recalculate lbs/gsm if a value exists
    if (values.lbs) {
      setValues((prev) => ({ ...prev, gsm: lbsToGsm(prev.lbs, newType), lbs: prev.lbs }));
    } else if (values.gsm) {
      setValues((prev) => ({ ...prev, lbs: gsmToLbs(prev.gsm, newType), gsm: prev.gsm }));
    }
  };

  const handleInputChange = (
    e: ChangeEvent<HTMLInputElement>,
    field: keyof ConverterValues
  ) => {
    if (updating) return;
    setUpdating(true);

    const newValue = e.target.value;
    let newValues: Partial<ConverterValues> = { [field]: newValue };

    if (newValue === '') { // If input is cleared, clear its counterpart
      if (field === 'lbs') newValues.gsm = '';
      else if (field === 'gsm') newValues.lbs = '';
      else if (field === 'pt') newValues.microns = '';
      else if (field === 'microns') newValues.pt = '';
      else if (field === 'inches') newValues.mm = '';
      else if (field === 'mm') newValues.inches = '';
    } else {
      if (field === 'lbs') newValues.gsm = lbsToGsm(newValue, paperType);
      else if (field === 'gsm') newValues.lbs = gsmToLbs(newValue, paperType);
      else if (field === 'pt') newValues.microns = ptToMicrons(newValue);
      else if (field === 'microns') newValues.pt = micronsToPt(newValue);
      else if (field === 'inches') newValues.mm = inToMm(newValue);
      else if (field === 'mm') newValues.inches = mmToIn(newValue);
    }

    setValues((prev) => ({ ...prev, ...newValues }));

    // Use a timeout to reset the updating flag, allowing UI to update
    setTimeout(() => setUpdating(false), 0);
  };

  return (
    <CardWrapper className="unit-converter-card">
      <h2 className="text-xl font-medium mb-4">Unit Converter</h2>

      <div className="space-y-4">
        <div>
          <label className="form-label">
            Paper Weight Type
          </label>
          <div className="flex space-x-4 mb-2">
            <label className="inline-flex items-center">
              <input
                type="radio"
                className="form-radio h-4 w-4"
                name="paperWeightType_innerText"
                value="Text"
                checked={paperType === 'Text'}
                onChange={handlePaperTypeChange}
              />
              <span className="ml-2 text-sm">Text</span>
            </label>
            <label className="inline-flex items-center">
              <input
                type="radio"
                className="form-radio h-4 w-4"
                name="paperWeightType_innerText"
                value="Cover"
                checked={paperType === 'Cover'}
                onChange={handlePaperTypeChange}
              />
              <span className="ml-2 text-sm">Cover</span>
            </label>
          </div>

          <div className="grid grid-cols-2 gap-3">
            <div>
              <label htmlFor="converter_lbs_innerText" className="form-label">
                Pounds (lbs)
              </label>
              <input
                type="number"
                id="converter_lbs_innerText"
                className="form-input"
                step="any"
                value={values.lbs}
                onChange={(e) => handleInputChange(e, 'lbs')}
                placeholder="e.g., 80"
              />
            </div>
            <div>
              <label htmlFor="converter_gsm_innerText" className="form-label">
                GSM (g/m²)
              </label>
              <input
                type="number"
                id="converter_gsm_innerText"
                className="form-input"
                step="any"
                value={values.gsm}
                onChange={(e) => handleInputChange(e, 'gsm')}
                placeholder="e.g., 120"
              />
            </div>
          </div>
        </div>

        <div>
          <label className="form-label">
            Thickness
          </label>
          <div className="grid grid-cols-2 gap-3 mb-2">
            <div>
              <label htmlFor="converter_pt_innerText" className="form-label">
                Points (pt)
              </label>
              <input
                type="number"
                id="converter_pt_innerText"
                className="form-input"
                step="any"
                value={values.pt}
                onChange={(e) => handleInputChange(e, 'pt')}
                placeholder="e.g., 8.5"
              />
            </div>
            <div>
              <label htmlFor="converter_microns_innerText" className="form-label">
                Microns (µm)
              </label>
              <input
                type="number"
                id="converter_microns_innerText"
                className="form-input"
                step="any"
                value={values.microns}
                onChange={(e) => handleInputChange(e, 'microns')}
                placeholder="e.g., 216"
              />
            </div>
          </div>

          <div className="grid grid-cols-2 gap-3">
            <div>
              <label htmlFor="converter_in_innerText" className="form-label">
                Inches (in)
              </label>
              <input
                type="number"
                id="converter_in_innerText"
                className="form-input"
                step="any"
                value={values.inches}
                onChange={(e) => handleInputChange(e, 'inches')}
                placeholder="e.g., 0.0085"
              />
            </div>
            <div>
              <label htmlFor="converter_mm_innerText" className="form-label">
                Millimeters (mm)
              </label>
              <input
                type="number"
                id="converter_mm_innerText"
                className="form-input"
                step="any"
                value={values.mm}
                onChange={(e) => handleInputChange(e, 'mm')}
                placeholder="e.g., 0.216"
              />
            </div>
          </div>
        </div>
      </div>
    </CardWrapper>
  );
};

export default UnitConverterCard;
