'use client';

import React, { useState, useEffect, useRef } from 'react';

interface TabProps {
  id: string;
  label: string;
  icon?: React.ReactNode;
}

interface TabsProps {
  tabs: TabProps[];
  activeTab: string;
  onTabChange: (tabId: string) => void;
  variant?: 'filled' | 'underlined' | 'pills';
  size?: 'sm' | 'md' | 'lg';
  fullWidth?: boolean;
  className?: string;
}

export default function Tabs({
  tabs,
  activeTab,
  onTabChange,
  variant = 'filled',
  size = 'md',
  fullWidth = false,
  className = '',
}: TabsProps) {
  const [indicatorStyle, setIndicatorStyle] = useState({});
  const tabsRef = useRef<(HTMLButtonElement | null)[]>([]);
  const containerRef = useRef<HTMLDivElement>(null);

  // Update the indicator position when the active tab changes
  useEffect(() => {
    const updateIndicator = () => {
      const activeIndex = tabs.findIndex(tab => tab.id === activeTab);
      if (activeIndex >= 0 && tabsRef.current[activeIndex]) {
        const activeTabElement = tabsRef.current[activeIndex];
        if (activeTabElement) {
          if (variant === 'underlined') {
            setIndicatorStyle({
              width: `${activeTabElement.offsetWidth}px`,
              transform: `translateX(${activeTabElement.offsetLeft}px)`,
            });
          } else if (variant === 'filled') {
            setIndicatorStyle({
              width: `${activeTabElement.offsetWidth}px`,
              height: `${activeTabElement.offsetHeight}px`,
              transform: `translateX(${activeTabElement.offsetLeft}px)`,
            });
          }
        }
      }
    };

    updateIndicator();
    // Add resize listener to update indicator on window resize
    window.addEventListener('resize', updateIndicator);
    return () => window.removeEventListener('resize', updateIndicator);
  }, [activeTab, tabs, variant]);

  // Size classes
  const getSizeClasses = () => {
    switch (size) {
      case 'sm':
        return 'text-sm py-1.5 px-3';
      case 'lg':
        return 'text-base py-3 px-6';
      case 'md':
      default:
        return 'text-base py-2 px-4';
    }
  };

  // Variant classes
  const getVariantClasses = () => {
    switch (variant) {
      case 'underlined':
        return 'bg-transparent hover:bg-neutral-100 dark:hover:bg-neutral-800';
      case 'pills':
        return 'rounded-xl hover:bg-white/50 dark:hover:bg-neutral-700/50';
      case 'filled':
      default:
        return 'rounded-t-lg';
    }
  };

  // Container classes
  const getContainerClasses = () => {
    switch (variant) {
      case 'underlined':
        return 'border-b border-neutral-200 dark:border-neutral-700';
      case 'pills':
        return 'p-1.5 bg-gradient-to-br from-white/40 via-white/20 to-white/40 dark:from-neutral-800/70 dark:via-neutral-900/50 dark:to-neutral-800/70 rounded-3xl backdrop-blur-2xl border border-white/50 dark:border-neutral-600/50 shadow-[0_8px_32px_rgba(0,0,0,0.12)] dark:shadow-[0_8px_32px_rgba(0,0,0,0.3)] relative overflow-hidden';
      case 'filled':
      default:
        return 'rounded-lg bg-white dark:bg-neutral-800 shadow-sm';
    }
  };

  return (
    <div
      ref={containerRef}
      className={`relative flex ${fullWidth ? 'w-full' : 'inline-flex'} ${getContainerClasses()} ${variant === 'pills' ? 'pills' : ''} ${className}`}
    >
      {variant === 'filled' && (
        <div
          className="absolute z-0 bg-white dark:bg-neutral-700 rounded-lg shadow-sm transition-all duration-300 ease-in-out"
          style={indicatorStyle}
        />
      )}

      {tabs.map((tab, index) => (
        <button
          key={tab.id}
          ref={el => (tabsRef.current[index] = el)}
          className={`
            relative z-10 flex items-center justify-center transition-all duration-500 ease-out group
            ${getSizeClasses()}
            ${getVariantClasses()}
            ${fullWidth ? 'flex-1' : ''}
            ${activeTab === tab.id
              ? variant === 'filled'
                ? 'text-primary font-semibold shadow-sm'
                : variant === 'pills'
                  ? 'bg-gradient-to-br from-white via-white/98 to-white/95 dark:from-neutral-700 dark:via-neutral-600 dark:to-neutral-700 text-primary font-bold shadow-[0_4px_20px_rgba(99,102,241,0.25)] dark:shadow-[0_4px_20px_rgba(99,102,241,0.4)] scale-105 border border-primary/40 dark:border-primary/60 backdrop-blur-sm relative overflow-hidden'
                  : 'text-primary font-semibold'
              : variant === 'pills'
                ? 'text-neutral-600 dark:text-neutral-300 hover:text-primary font-medium hover:scale-[1.02] hover:bg-white/40 dark:hover:bg-neutral-700/40 hover:shadow-lg transition-all duration-300'
                : 'text-neutral-600 dark:text-neutral-400 hover:text-primary font-medium hover:scale-102'
            }
          `}
          onClick={() => onTabChange(tab.id)}
          role="tab"
          aria-selected={activeTab === tab.id}
          tabIndex={activeTab === tab.id ? 0 : -1}
        >
          {tab.icon && <span className="mr-2 h-4 w-4 flex-shrink-0">{tab.icon}</span>}
          <span className="whitespace-nowrap">{tab.label}</span>
        </button>
      ))}

      {variant === 'underlined' && (
        <div
          className="absolute bottom-0 h-0.5 bg-primary transition-all duration-300 ease-in-out"
          style={indicatorStyle}
        />
      )}
    </div>
  );
}
